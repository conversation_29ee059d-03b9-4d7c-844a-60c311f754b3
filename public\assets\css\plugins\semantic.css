/*
 * # Semantic UI - 2.3.1
 * https://github.com/Semantic-Org/Semantic-UI
 * http://www.semantic-ui.com/
 *
 */
/*--------------------
       Inverted
---------------------*/
/* Standard */
.ui.inverted.input > input {
  border: none; }

/*--------------------
        Fluid
---------------------*/
.ui.fluid.input {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex; }

.ui.fluid.input > input {
  width: 0px !important; }

/*--------------------
        Size
---------------------*/
.ui.mini.input {
  font-size: 0.78571429em; }

.ui.small.input {
  font-size: 0.92857143em; }

.ui.input {
  font-size: 1em; }

.ui.large.input {
  font-size: 1.14285714em; }

.ui.big.input {
  font-size: 1.28571429em; }

.ui.huge.input {
  font-size: 1.42857143em; }

.ui.massive.input {
  font-size: 1.71428571em; }

/*******************************
         Theme Overrides
*******************************/
/*******************************
         Site Overrides
*******************************/
/*!
 * # Semantic UI 2.3.1 - Label
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */
/*******************************
            Label
*******************************/
.ui.label {
  display: inline-block;
  line-height: 1;
  vertical-align: baseline;
  margin: 0em 0.14285714em;
  background-color: #E8E8E8;
  background-image: none;
  padding: 0.5833em 0.833em;
  color: rgba(0, 0, 0, 0.6);
  text-transform: none;
  font-weight: bold;
  border: 0px solid transparent;
  border-radius: 0.28571429rem;
  -webkit-transition: background 0.1s ease;
  transition: background 0.1s ease; }

.ui.label:first-child {
  margin-left: 0em; }

.ui.label:last-child {
  margin-right: 0em; }

/* Link */
a.ui.label {
  cursor: pointer; }

/* Inside Link */
.ui.label > a {
  cursor: pointer;
  color: inherit;
  opacity: 0.5;
  -webkit-transition: 0.1s opacity ease;
  transition: 0.1s opacity ease; }

.ui.label > a:hover {
  opacity: 1; }

/* Image */
.ui.label > img {
  width: auto !important;
  vertical-align: middle;
  height: 2.1666em !important; }

/* Icon */
.ui.label > .icon {
  width: auto;
  margin: 0em 0.75em 0em 0em; }

/* Detail */
.ui.label > .detail {
  display: inline-block;
  vertical-align: top;
  font-weight: bold;
  margin-left: 1em;
  opacity: 0.8; }

.ui.label > .detail .icon {
  margin: 0em 0.25em 0em 0em; }

/* Removable label */
.ui.label > .close.icon,
.ui.label > .delete.icon {
  cursor: pointer;
  margin-right: 0em;
  margin-left: 0.5em;
  font-size: 0.92857143em;
  opacity: 0.5;
  -webkit-transition: background 0.1s ease;
  transition: background 0.1s ease; }

.ui.label > .delete.icon:hover {
  opacity: 1; }

/*-------------------
       Group
--------------------*/
.ui.labels > .label {
  margin: 0em 0.5em 0.5em 0em; }

/*-------------------
       Coupling
--------------------*/
.ui.header > .ui.label {
  margin-top: -0.29165em; }

/* Remove border radius on attached segment */
.ui.attached.segment > .ui.top.left.attached.label,
.ui.bottom.attached.segment > .ui.top.left.attached.label {
  border-top-left-radius: 0; }

.ui.attached.segment > .ui.top.right.attached.label,
.ui.bottom.attached.segment > .ui.top.right.attached.label {
  border-top-right-radius: 0; }

.ui.top.attached.segment > .ui.bottom.left.attached.label {
  border-bottom-left-radius: 0; }

.ui.top.attached.segment > .ui.bottom.right.attached.label {
  border-bottom-right-radius: 0; }

/* Padding on next content after a label */
.ui.top.attached.label:first-child + :not(.attached),
.ui.top.attached.label + [class*="right floated"] + * {
  margin-top: 2rem !important; }

.ui.bottom.attached.label:first-child ~ :last-child:not(.attached) {
  margin-top: 0em;
  margin-bottom: 2rem !important; }

/*******************************
             Types
*******************************/
.ui.image.label {
  width: auto !important;
  margin-top: 0em;
  margin-bottom: 0em;
  max-width: 9999px;
  vertical-align: baseline;
  text-transform: none;
  background: #E8E8E8;
  padding: 0.5833em 0.833em 0.5833em 0.5em;
  border-radius: 0.28571429rem;
  -webkit-box-shadow: none;
  box-shadow: none; }

.ui.image.label img {
  display: inline-block;
  vertical-align: top;
  height: 2.1666em;
  margin: -0.5833em 0.5em -0.5833em -0.5em;
  border-radius: 0.28571429rem 0em 0em 0.28571429rem; }

.ui.image.label .detail {
  background: rgba(0, 0, 0, 0.1);
  margin: -0.5833em -0.833em -0.5833em 0.5em;
  padding: 0.5833em 0.833em;
  border-radius: 0em 0.28571429rem 0.28571429rem 0em; }

/*-------------------
         Tag
--------------------*/
.ui.tag.labels .label,
.ui.tag.label {
  margin-left: 1em;
  position: relative;
  padding-left: 1.5em;
  padding-right: 1.5em;
  border-radius: 0em 0.28571429rem 0.28571429rem 0em;
  -webkit-transition: none;
  transition: none; }

.ui.tag.labels .label:before,
.ui.tag.label:before {
  position: absolute;
  -webkit-transform: translateY(-50%) translateX(50%) rotate(-45deg);
  transform: translateY(-50%) translateX(50%) rotate(-45deg);
  top: 50%;
  right: 100%;
  content: '';
  background-color: inherit;
  background-image: none;
  width: 1.56em;
  height: 1.56em;
  -webkit-transition: none;
  transition: none; }

.ui.tag.labels .label:after,
.ui.tag.label:after {
  position: absolute;
  content: '';
  top: 50%;
  left: -0.25em;
  margin-top: -0.25em;
  background-color: #FFFFFF !important;
  width: 0.5em;
  height: 0.5em;
  -webkit-box-shadow: 0 -1px 1px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 -1px 1px 0 rgba(0, 0, 0, 0.3);
  border-radius: 500rem; }

/*-------------------
    Corner Label
--------------------*/
.ui.corner.label {
  position: absolute;
  top: 0em;
  right: 0em;
  margin: 0em;
  padding: 0em;
  text-align: center;
  border-color: #E8E8E8;
  width: 4em;
  height: 4em;
  z-index: 1;
  -webkit-transition: border-color 0.1s ease;
  transition: border-color 0.1s ease; }

/* Icon Label */
.ui.corner.label {
  background-color: transparent !important; }

.ui.corner.label:after {
  position: absolute;
  content: "";
  right: 0em;
  top: 0em;
  z-index: -1;
  width: 0em;
  height: 0em;
  background-color: transparent !important;
  border-top: 0em solid transparent;
  border-right: 4em solid transparent;
  border-bottom: 4em solid transparent;
  border-left: 0em solid transparent;
  border-right-color: inherit;
  -webkit-transition: border-color 0.1s ease;
  transition: border-color 0.1s ease; }

.ui.corner.label .icon {
  cursor: default;
  position: relative;
  top: 0.64285714em;
  left: 0.78571429em;
  font-size: 1.14285714em;
  margin: 0em; }

/* Left Corner */
.ui.left.corner.label,
.ui.left.corner.label:after {
  right: auto;
  left: 0em; }

.ui.left.corner.label:after {
  border-top: 4em solid transparent;
  border-right: 4em solid transparent;
  border-bottom: 0em solid transparent;
  border-left: 0em solid transparent;
  border-top-color: inherit; }

.ui.left.corner.label .icon {
  left: -0.78571429em; }

/* Segment */
.ui.segment > .ui.corner.label {
  top: -1px;
  right: -1px; }

.ui.segment > .ui.left.corner.label {
  right: auto;
  left: -1px; }

/*-------------------
       Ribbon
--------------------*/
.ui.ribbon.label {
  position: relative;
  margin: 0em;
  min-width: -webkit-max-content;
  min-width: -moz-max-content;
  min-width: max-content;
  border-radius: 0em 0.28571429rem 0.28571429rem 0em;
  border-color: rgba(0, 0, 0, 0.15); }

.ui.ribbon.label:after {
  position: absolute;
  content: '';
  top: 100%;
  left: 0%;
  background-color: transparent !important;
  border-style: solid;
  border-width: 0em 1.2em 1.2em 0em;
  border-color: transparent;
  border-right-color: inherit;
  width: 0em;
  height: 0em; }

/* Positioning */
.ui.ribbon.label {
  left: calc( -1rem  -  1.2em);
  margin-right: -1.2em;
  padding-left: calc( 1rem  +  1.2em);
  padding-right: 1.2em; }

.ui[class*="right ribbon"].label {
  left: calc(100% +  1rem  +  1.2em);
  padding-left: 1.2em;
  padding-right: calc( 1rem  +  1.2em); }

/* Right Ribbon */
.ui[class*="right ribbon"].label {
  text-align: left;
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
  border-radius: 0.28571429rem 0em 0em 0.28571429rem; }

.ui[class*="right ribbon"].label:after {
  left: auto;
  right: 0%;
  border-style: solid;
  border-width: 1.2em 1.2em 0em 0em;
  border-color: transparent;
  border-top-color: inherit; }

/* Inside Table */
.ui.image > .ribbon.label,
.ui.card .image > .ribbon.label {
  position: absolute;
  top: 1rem; }

.ui.card .image > .ui.ribbon.label,
.ui.image > .ui.ribbon.label {
  left: calc( 0.05rem  -  1.2em); }

.ui.card .image > .ui[class*="right ribbon"].label,
.ui.image > .ui[class*="right ribbon"].label {
  left: calc(100% +  -0.05rem  +  1.2em);
  padding-left: 0.833em; }

/* Inside Table */
.ui.table td > .ui.ribbon.label {
  left: calc( -0.78571429em  -  1.2em); }

.ui.table td > .ui[class*="right ribbon"].label {
  left: calc(100% +  0.78571429em  +  1.2em);
  padding-left: 0.833em; }

/*-------------------
      Attached
--------------------*/
.ui[class*="top attached"].label,
.ui.attached.label {
  width: 100%;
  position: absolute;
  margin: 0em;
  top: 0em;
  left: 0em;
  padding: 0.75em 1em;
  border-radius: 0.21428571rem 0.21428571rem 0em 0em; }

.ui[class*="bottom attached"].label {
  top: auto;
  bottom: 0em;
  border-radius: 0em 0em 0.21428571rem 0.21428571rem; }

.ui[class*="top left attached"].label {
  width: auto;
  margin-top: 0em !important;
  border-radius: 0.21428571rem 0em 0.28571429rem 0em; }

.ui[class*="top right attached"].label {
  width: auto;
  left: auto;
  right: 0em;
  border-radius: 0em 0.21428571rem 0em 0.28571429rem; }

.ui[class*="bottom left attached"].label {
  width: auto;
  top: auto;
  bottom: 0em;
  border-radius: 0em 0.28571429rem 0em 0.21428571rem; }

.ui[class*="bottom right attached"].label {
  top: auto;
  bottom: 0em;
  left: auto;
  right: 0em;
  width: auto;
  border-radius: 0.28571429rem 0em 0.21428571rem 0em; }

/*******************************
             States
*******************************/
/*-------------------
      Disabled
--------------------*/
.ui.label.disabled {
  opacity: 0.5; }

/*-------------------
        Hover
--------------------*/
a.ui.labels .label:hover,
a.ui.label:hover {
  background-color: #E0E0E0;
  border-color: #E0E0E0;
  background-image: none;
  color: rgba(0, 0, 0, 0.8); }

.ui.labels a.label:hover:before,
a.ui.label:hover:before {
  color: rgba(0, 0, 0, 0.8); }

/*-------------------
        Active
--------------------*/
.ui.active.label {
  background-color: #D0D0D0;
  border-color: #D0D0D0;
  background-image: none;
  color: rgba(0, 0, 0, 0.95); }

.ui.active.label:before {
  background-color: #D0D0D0;
  background-image: none;
  color: rgba(0, 0, 0, 0.95); }

/*-------------------
     Active Hover
--------------------*/
a.ui.labels .active.label:hover,
a.ui.active.label:hover {
  background-color: #C8C8C8;
  border-color: #C8C8C8;
  background-image: none;
  color: rgba(0, 0, 0, 0.95); }

.ui.labels a.active.label:ActiveHover:before,
a.ui.active.label:ActiveHover:before {
  background-color: #C8C8C8;
  background-image: none;
  color: rgba(0, 0, 0, 0.95); }

/*-------------------
      Visible
--------------------*/
.ui.labels.visible .label,
.ui.label.visible:not(.dropdown) {
  display: inline-block !important; }

/*-------------------
      Hidden
--------------------*/
.ui.labels.hidden .label,
.ui.label.hidden {
  display: none !important; }

/*******************************
           Variations
*******************************/
/*-------------------
       Colors
--------------------*/
/*--- Red ---*/
.ui.red.labels .label,
.ui.red.label {
  background-color: #DB2828 !important;
  border-color: #DB2828 !important;
  color: #FFFFFF !important; }

/* Link */
.ui.red.labels .label:hover,
a.ui.red.label:hover {
  background-color: #d01919 !important;
  border-color: #d01919 !important;
  color: #FFFFFF !important; }

/* Corner */
.ui.red.corner.label,
.ui.red.corner.label:hover {
  background-color: transparent !important; }

/* Ribbon */
.ui.red.ribbon.label {
  border-color: #b21e1e !important; }

/* Basic */
.ui.basic.red.label {
  background-color: #FFFFFF !important;
  color: #DB2828 !important;
  border-color: #DB2828 !important; }

.ui.basic.red.labels a.label:hover,
a.ui.basic.red.label:hover {
  background-color: #FFFFFF !important;
  color: #d01919 !important;
  border-color: #d01919 !important; }

/*--- Orange ---*/
.ui.orange.labels .label,
.ui.orange.label {
  background-color: #F2711C !important;
  border-color: #F2711C !important;
  color: #FFFFFF !important; }

/* Link */
.ui.orange.labels .label:hover,
a.ui.orange.label:hover {
  background-color: #f26202 !important;
  border-color: #f26202 !important;
  color: #FFFFFF !important; }

/* Corner */
.ui.orange.corner.label,
.ui.orange.corner.label:hover {
  background-color: transparent !important; }

/* Ribbon */
.ui.orange.ribbon.label {
  border-color: #cf590c !important; }

/* Basic */
.ui.basic.orange.label {
  background-color: #FFFFFF !important;
  color: #F2711C !important;
  border-color: #F2711C !important; }

.ui.basic.orange.labels a.label:hover,
a.ui.basic.orange.label:hover {
  background-color: #FFFFFF !important;
  color: #f26202 !important;
  border-color: #f26202 !important; }

/*--- Yellow ---*/
.ui.yellow.labels .label,
.ui.yellow.label {
  background-color: #FBBD08 !important;
  border-color: #FBBD08 !important;
  color: #FFFFFF !important; }

/* Link */
.ui.yellow.labels .label:hover,
a.ui.yellow.label:hover {
  background-color: #eaae00 !important;
  border-color: #eaae00 !important;
  color: #FFFFFF !important; }

/* Corner */
.ui.yellow.corner.label,
.ui.yellow.corner.label:hover {
  background-color: transparent !important; }

/* Ribbon */
.ui.yellow.ribbon.label {
  border-color: #cd9903 !important; }

/* Basic */
.ui.basic.yellow.label {
  background-color: #FFFFFF !important;
  color: #FBBD08 !important;
  border-color: #FBBD08 !important; }

.ui.basic.yellow.labels a.label:hover,
a.ui.basic.yellow.label:hover {
  background-color: #FFFFFF !important;
  color: #eaae00 !important;
  border-color: #eaae00 !important; }

/*--- Olive ---*/
.ui.olive.labels .label,
.ui.olive.label {
  background-color: #B5CC18 !important;
  border-color: #B5CC18 !important;
  color: #FFFFFF !important; }

/* Link */
.ui.olive.labels .label:hover,
a.ui.olive.label:hover {
  background-color: #a7bd0d !important;
  border-color: #a7bd0d !important;
  color: #FFFFFF !important; }

/* Corner */
.ui.olive.corner.label,
.ui.olive.corner.label:hover {
  background-color: transparent !important; }

/* Ribbon */
.ui.olive.ribbon.label {
  border-color: #198f35 !important; }

/* Basic */
.ui.basic.olive.label {
  background-color: #FFFFFF !important;
  color: #B5CC18 !important;
  border-color: #B5CC18 !important; }

.ui.basic.olive.labels a.label:hover,
a.ui.basic.olive.label:hover {
  background-color: #FFFFFF !important;
  color: #a7bd0d !important;
  border-color: #a7bd0d !important; }

/*--- Green ---*/
.ui.green.labels .label,
.ui.green.label {
  background-color: #21BA45 !important;
  border-color: #21BA45 !important;
  color: #FFFFFF !important; }

/* Link */
.ui.green.labels .label:hover,
a.ui.green.label:hover {
  background-color: #16ab39 !important;
  border-color: #16ab39 !important;
  color: #FFFFFF !important; }

/* Corner */
.ui.green.corner.label,
.ui.green.corner.label:hover {
  background-color: transparent !important; }

/* Ribbon */
.ui.green.ribbon.label {
  border-color: #198f35 !important; }

/* Basic */
.ui.basic.green.label {
  background-color: #FFFFFF !important;
  color: #21BA45 !important;
  border-color: #21BA45 !important; }

.ui.basic.green.labels a.label:hover,
a.ui.basic.green.label:hover {
  background-color: #FFFFFF !important;
  color: #16ab39 !important;
  border-color: #16ab39 !important; }

/*--- Teal ---*/
.ui.teal.labels .label,
.ui.teal.label {
  background-color: #00B5AD !important;
  border-color: #00B5AD !important;
  color: #FFFFFF !important; }

/* Link */
.ui.teal.labels .label:hover,
a.ui.teal.label:hover {
  background-color: #009c95 !important;
  border-color: #009c95 !important;
  color: #FFFFFF !important; }

/* Corner */
.ui.teal.corner.label,
.ui.teal.corner.label:hover {
  background-color: transparent !important; }

/* Ribbon */
.ui.teal.ribbon.label {
  border-color: #00827c !important; }

/* Basic */
.ui.basic.teal.label {
  background-color: #FFFFFF !important;
  color: #00B5AD !important;
  border-color: #00B5AD !important; }

.ui.basic.teal.labels a.label:hover,
a.ui.basic.teal.label:hover {
  background-color: #FFFFFF !important;
  color: #009c95 !important;
  border-color: #009c95 !important; }

/*--- Blue ---*/
.ui.blue.labels .label,
.ui.blue.label {
  background-color: #2185D0 !important;
  border-color: #2185D0 !important;
  color: #FFFFFF !important; }

/* Link */
.ui.blue.labels .label:hover,
a.ui.blue.label:hover {
  background-color: #1678c2 !important;
  border-color: #1678c2 !important;
  color: #FFFFFF !important; }

/* Corner */
.ui.blue.corner.label,
.ui.blue.corner.label:hover {
  background-color: transparent !important; }

/* Ribbon */
.ui.blue.ribbon.label {
  border-color: #1a69a4 !important; }

/* Basic */
.ui.basic.blue.label {
  background-color: #FFFFFF !important;
  color: #2185D0 !important;
  border-color: #2185D0 !important; }

.ui.basic.blue.labels a.label:hover,
a.ui.basic.blue.label:hover {
  background-color: #FFFFFF !important;
  color: #1678c2 !important;
  border-color: #1678c2 !important; }

/*--- Violet ---*/
.ui.violet.labels .label,
.ui.violet.label {
  background-color: #6435C9 !important;
  border-color: #6435C9 !important;
  color: #FFFFFF !important; }

/* Link */
.ui.violet.labels .label:hover,
a.ui.violet.label:hover {
  background-color: #5829bb !important;
  border-color: #5829bb !important;
  color: #FFFFFF !important; }

/* Corner */
.ui.violet.corner.label,
.ui.violet.corner.label:hover {
  background-color: transparent !important; }

/* Ribbon */
.ui.violet.ribbon.label {
  border-color: #502aa1 !important; }

/* Basic */
.ui.basic.violet.label {
  background-color: #FFFFFF !important;
  color: #6435C9 !important;
  border-color: #6435C9 !important; }

.ui.basic.violet.labels a.label:hover,
a.ui.basic.violet.label:hover {
  background-color: #FFFFFF !important;
  color: #5829bb !important;
  border-color: #5829bb !important; }

/*--- Purple ---*/
.ui.purple.labels .label,
.ui.purple.label {
  background-color: #A333C8 !important;
  border-color: #A333C8 !important;
  color: #FFFFFF !important; }

/* Link */
.ui.purple.labels .label:hover,
a.ui.purple.label:hover {
  background-color: #9627ba !important;
  border-color: #9627ba !important;
  color: #FFFFFF !important; }

/* Corner */
.ui.purple.corner.label,
.ui.purple.corner.label:hover {
  background-color: transparent !important; }

/* Ribbon */
.ui.purple.ribbon.label {
  border-color: #82299f !important; }

/* Basic */
.ui.basic.purple.label {
  background-color: #FFFFFF !important;
  color: #A333C8 !important;
  border-color: #A333C8 !important; }

.ui.basic.purple.labels a.label:hover,
a.ui.basic.purple.label:hover {
  background-color: #FFFFFF !important;
  color: #9627ba !important;
  border-color: #9627ba !important; }

/*--- Pink ---*/
.ui.pink.labels .label,
.ui.pink.label {
  background-color: #E03997 !important;
  border-color: #E03997 !important;
  color: #FFFFFF !important; }

/* Link */
.ui.pink.labels .label:hover,
a.ui.pink.label:hover {
  background-color: #e61a8d !important;
  border-color: #e61a8d !important;
  color: #FFFFFF !important; }

/* Corner */
.ui.pink.corner.label,
.ui.pink.corner.label:hover {
  background-color: transparent !important; }

/* Ribbon */
.ui.pink.ribbon.label {
  border-color: #c71f7e !important; }

/* Basic */
.ui.basic.pink.label {
  background-color: #FFFFFF !important;
  color: #E03997 !important;
  border-color: #E03997 !important; }

.ui.basic.pink.labels a.label:hover,
a.ui.basic.pink.label:hover {
  background-color: #FFFFFF !important;
  color: #e61a8d !important;
  border-color: #e61a8d !important; }

/*--- Brown ---*/
.ui.brown.labels .label,
.ui.brown.label {
  background-color: #A5673F !important;
  border-color: #A5673F !important;
  color: #FFFFFF !important; }

/* Link */
.ui.brown.labels .label:hover,
a.ui.brown.label:hover {
  background-color: #975b33 !important;
  border-color: #975b33 !important;
  color: #FFFFFF !important; }

/* Corner */
.ui.brown.corner.label,
.ui.brown.corner.label:hover {
  background-color: transparent !important; }

/* Ribbon */
.ui.brown.ribbon.label {
  border-color: #805031 !important; }

/* Basic */
.ui.basic.brown.label {
  background-color: #FFFFFF !important;
  color: #A5673F !important;
  border-color: #A5673F !important; }

.ui.basic.brown.labels a.label:hover,
a.ui.basic.brown.label:hover {
  background-color: #FFFFFF !important;
  color: #975b33 !important;
  border-color: #975b33 !important; }

/*--- Grey ---*/
.ui.grey.labels .label,
.ui.grey.label {
  background-color: #767676 !important;
  border-color: #767676 !important;
  color: #FFFFFF !important; }

/* Link */
.ui.grey.labels .label:hover,
a.ui.grey.label:hover {
  background-color: #838383 !important;
  border-color: #838383 !important;
  color: #FFFFFF !important; }

/* Corner */
.ui.grey.corner.label,
.ui.grey.corner.label:hover {
  background-color: transparent !important; }

/* Ribbon */
.ui.grey.ribbon.label {
  border-color: #805031 !important; }

/* Basic */
.ui.basic.grey.label {
  background-color: #FFFFFF !important;
  color: #767676 !important;
  border-color: #767676 !important; }

.ui.basic.grey.labels a.label:hover,
a.ui.basic.grey.label:hover {
  background-color: #FFFFFF !important;
  color: #838383 !important;
  border-color: #838383 !important; }

/*--- Black ---*/
.ui.black.labels .label,
.ui.black.label {
  background-color: #1B1C1D !important;
  border-color: #1B1C1D !important;
  color: #FFFFFF !important; }

/* Link */
.ui.black.labels .label:hover,
a.ui.black.label:hover {
  background-color: #27292a !important;
  border-color: #27292a !important;
  color: #FFFFFF !important; }

/* Corner */
.ui.black.corner.label,
.ui.black.corner.label:hover {
  background-color: transparent !important; }

/* Ribbon */
.ui.black.ribbon.label {
  border-color: #805031 !important; }

/* Basic */
.ui.basic.black.label {
  background-color: #FFFFFF !important;
  color: #1B1C1D !important;
  border-color: #1B1C1D !important; }

.ui.basic.black.labels a.label:hover,
a.ui.basic.black.label:hover {
  background-color: #FFFFFF !important;
  color: #27292a !important;
  border-color: #27292a !important; }

/*-------------------
        Basic
--------------------*/
.ui.basic.label {
  background: none #FFFFFF;
  border: 1px solid rgba(34, 36, 38, 0.15);
  color: rgba(0, 0, 0, 0.87);
  -webkit-box-shadow: none;
  box-shadow: none; }

/* Link */
a.ui.basic.label:hover {
  text-decoration: none;
  background: none #FFFFFF;
  color: #1e70bf;
  -webkit-box-shadow: 1px solid rgba(34, 36, 38, 0.15);
  box-shadow: 1px solid rgba(34, 36, 38, 0.15);
  -webkit-box-shadow: none;
  box-shadow: none; }

/* Pointing */
.ui.basic.pointing.label:before {
  border-color: inherit; }

/*-------------------
       Fluid
--------------------*/
.ui.label.fluid,
.ui.fluid.labels > .label {
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box; }

/*-------------------
       Inverted
--------------------*/
.ui.inverted.labels .label,
.ui.inverted.label {
  color: rgba(255, 255, 255, 0.9) !important; }

/*-------------------
     Horizontal
--------------------*/
.ui.horizontal.labels .label,
.ui.horizontal.label {
  margin: 0em 0.5em 0em 0em;
  padding: 0.4em 0.833em;
  min-width: 3em;
  text-align: center; }

/*-------------------
       Circular
--------------------*/
.ui.circular.labels .label,
.ui.circular.label {
  min-width: 2em;
  min-height: 2em;
  padding: 0.5em !important;
  line-height: 1em;
  text-align: center;
  border-radius: 500rem; }

.ui.empty.circular.labels .label,
.ui.empty.circular.label {
  min-width: 0em;
  min-height: 0em;
  overflow: hidden;
  width: 0.5em;
  height: 0.5em;
  vertical-align: baseline; }

/*-------------------
       Pointing
--------------------*/
.ui.pointing.label {
  position: relative; }

.ui.attached.pointing.label {
  position: absolute; }

.ui.pointing.label:before {
  background-color: inherit;
  background-image: inherit;
  border-width: none;
  border-style: solid;
  border-color: inherit; }

/* Arrow */
.ui.pointing.label:before {
  position: absolute;
  content: '';
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  background-image: none;
  z-index: 2;
  width: 0.6666em;
  height: 0.6666em;
  -webkit-transition: background 0.1s ease;
  transition: background 0.1s ease; }

/*--- Above ---*/
.ui.pointing.label,
.ui[class*="pointing above"].label {
  margin-top: 1em; }

.ui.pointing.label:before,
.ui[class*="pointing above"].label:before {
  border-width: 1px 0px 0px 1px;
  -webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg);
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  top: 0%;
  left: 50%; }

/*--- Below ---*/
.ui[class*="bottom pointing"].label,
.ui[class*="pointing below"].label {
  margin-top: 0em;
  margin-bottom: 1em; }

.ui[class*="bottom pointing"].label:before,
.ui[class*="pointing below"].label:before {
  border-width: 0px 1px 1px 0px;
  top: auto;
  right: auto;
  -webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg);
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  top: 100%;
  left: 50%; }

/*--- Left ---*/
.ui[class*="left pointing"].label {
  margin-top: 0em;
  margin-left: 0.6666em; }

.ui[class*="left pointing"].label:before {
  border-width: 0px 0px 1px 1px;
  -webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg);
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  bottom: auto;
  right: auto;
  top: 50%;
  left: 0em; }

/*--- Right ---*/
.ui[class*="right pointing"].label {
  margin-top: 0em;
  margin-right: 0.6666em; }

.ui[class*="right pointing"].label:before {
  border-width: 1px 1px 0px 0px;
  -webkit-transform: translateX(50%) translateY(-50%) rotate(45deg);
  transform: translateX(50%) translateY(-50%) rotate(45deg);
  top: 50%;
  right: 0%;
  bottom: auto;
  left: auto; }

/* Basic Pointing */
/*--- Above ---*/
.ui.basic.pointing.label:before,
.ui.basic[class*="pointing above"].label:before {
  margin-top: -1px; }

/*--- Below ---*/
.ui.basic[class*="bottom pointing"].label:before,
.ui.basic[class*="pointing below"].label:before {
  bottom: auto;
  top: 100%;
  margin-top: 1px; }

/*--- Left ---*/
.ui.basic[class*="left pointing"].label:before {
  top: 50%;
  left: -1px; }

/*--- Right ---*/
.ui.basic[class*="right pointing"].label:before {
  top: 50%;
  right: -1px; }

/*------------------
   Floating Label
-------------------*/
.ui.floating.label {
  position: absolute;
  z-index: 100;
  top: -1em;
  left: 100%;
  margin: 0em 0em 0em -1.5em !important; }

/*-------------------
        Sizes
--------------------*/
.ui.mini.labels .label,
.ui.mini.label {
  font-size: 0.64285714rem; }

.ui.tiny.labels .label,
.ui.tiny.label {
  font-size: 0.71428571rem; }

.ui.small.labels .label,
.ui.small.label {
  font-size: 0.78571429rem; }

.ui.labels .label,
.ui.label {
  font-size: 0.85714286rem; }

.ui.large.labels .label,
.ui.large.label {
  font-size: 1rem; }

.ui.big.labels .label,
.ui.big.label {
  font-size: 1.28571429rem; }

.ui.huge.labels .label,
.ui.huge.label {
  font-size: 1.42857143rem; }

.ui.massive.labels .label,
.ui.massive.label {
  font-size: 1.71428571rem; }

/*******************************
         Theme Overrides
*******************************/
/*******************************
         Site Overrides
*******************************/
/*!
 * # Semantic UI 2.3.1 - List
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */
/*******************************
            List
*******************************/
ul.ui.list,
ol.ui.list,
.ui.list {
  list-style-type: none;
  margin: 1em 0em;
  padding: 0em 0em; }

ul.ui.list:first-child,
ol.ui.list:first-child,
.ui.list:first-child {
  margin-top: 0em;
  padding-top: 0em; }

ul.ui.list:last-child,
ol.ui.list:last-child,
.ui.list:last-child {
  margin-bottom: 0em;
  padding-bottom: 0em; }

/*******************************
            Content
*******************************/
/* List Item */
ul.ui.list li,
ol.ui.list li,
.ui.list > .item,
.ui.list .list > .item {
  display: list-item;
  table-layout: fixed;
  list-style-type: none;
  list-style-position: outside;
  padding: 0.21428571em 0em;
  line-height: 1.14285714em; }

ul.ui.list > li:first-child:after,
ol.ui.list > li:first-child:after,
.ui.list > .list > .item,
.ui.list > .item:after {
  content: '';
  display: block;
  height: 0;
  clear: both;
  visibility: hidden; }

ul.ui.list li:first-child,
ol.ui.list li:first-child,
.ui.list .list > .item:first-child,
.ui.list > .item:first-child {
  padding-top: 0em; }

ul.ui.list li:last-child,
ol.ui.list li:last-child,
.ui.list .list > .item:last-child,
.ui.list > .item:last-child {
  padding-bottom: 0em; }

/* Child List */
ul.ui.list ul,
ol.ui.list ol,
.ui.list .list {
  clear: both;
  margin: 0em;
  padding: 0.75em 0em 0.25em 0.5em; }

/* Child Item */
ul.ui.list ul li,
ol.ui.list ol li,
.ui.list .list > .item {
  padding: 0.14285714em 0em;
  line-height: inherit; }

/* Icon */
.ui.list .list > .item > i.icon,
.ui.list > .item > i.icon {
  display: table-cell;
  margin: 0em;
  padding-top: 0em;
  padding-right: 0.28571429em;
  vertical-align: top;
  -webkit-transition: color 0.1s ease;
  transition: color 0.1s ease; }

.ui.list .list > .item > i.icon:only-child,
.ui.list > .item > i.icon:only-child {
  display: inline-block;
  vertical-align: top; }

/* Image */
.ui.list .list > .item > .image,
.ui.list > .item > .image {
  display: table-cell;
  background-color: transparent;
  margin: 0em;
  vertical-align: top; }

.ui.list .list > .item > .image:not(:only-child):not(img),
.ui.list > .item > .image:not(:only-child):not(img) {
  padding-right: 0.5em; }

.ui.list .list > .item > .image img,
.ui.list > .item > .image img {
  vertical-align: top; }

.ui.list .list > .item > img.image,
.ui.list .list > .item > .image:only-child,
.ui.list > .item > img.image,
.ui.list > .item > .image:only-child {
  display: inline-block; }

/* Content */
.ui.list .list > .item > .content,
.ui.list > .item > .content {
  line-height: 1.14285714em; }

.ui.list .list > .item > .image + .content,
.ui.list .list > .item > .icon + .content,
.ui.list > .item > .image + .content,
.ui.list > .item > .icon + .content {
  display: table-cell;
  padding: 0em 0em 0em 0.5em;
  vertical-align: top; }

.ui.list .list > .item > img.image + .content,
.ui.list > .item > img.image + .content {
  display: inline-block; }

.ui.list .list > .item > .content > .list,
.ui.list > .item > .content > .list {
  margin-left: 0em;
  padding-left: 0em; }

/* Header */
.ui.list .list > .item .header,
.ui.list > .item .header {
  display: block;
  margin: 0em;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.87); }

/* Description */
.ui.list .list > .item .description,
.ui.list > .item .description {
  display: block;
  color: rgba(0, 0, 0, 0.7); }

/* Child Link */
.ui.list > .item a,
.ui.list .list > .item a {
  cursor: pointer; }

/* Linking Item */
.ui.list .list > a.item,
.ui.list > a.item {
  cursor: pointer;
  color: #4183C4; }

.ui.list .list > a.item:hover,
.ui.list > a.item:hover {
  color: #1e70bf; }

/* Linked Item Icons */
.ui.list .list > a.item i.icon,
.ui.list > a.item i.icon {
  color: rgba(0, 0, 0, 0.4); }

/* Header Link */
.ui.list .list > .item a.header,
.ui.list > .item a.header {
  cursor: pointer;
  color: #4183C4 !important; }

.ui.list .list > .item a.header:hover,
.ui.list > .item a.header:hover {
  color: #1e70bf !important; }

/* Floated Content */
.ui[class*="left floated"].list {
  float: left; }

.ui[class*="right floated"].list {
  float: right; }

.ui.list .list > .item [class*="left floated"],
.ui.list > .item [class*="left floated"] {
  float: left;
  margin: 0em 1em 0em 0em; }

.ui.list .list > .item [class*="right floated"],
.ui.list > .item [class*="right floated"] {
  float: right;
  margin: 0em 0em 0em 1em; }

/*******************************
            Coupling
*******************************/
.ui.menu .ui.list > .item,
.ui.menu .ui.list .list > .item {
  display: list-item;
  table-layout: fixed;
  background-color: transparent;
  list-style-type: none;
  list-style-position: outside;
  padding: 0.21428571em 0em;
  line-height: 1.14285714em; }

.ui.menu .ui.list .list > .item:before,
.ui.menu .ui.list > .item:before {
  border: none;
  background: none; }

.ui.menu .ui.list .list > .item:first-child,
.ui.menu .ui.list > .item:first-child {
  padding-top: 0em; }

.ui.menu .ui.list .list > .item:last-child,
.ui.menu .ui.list > .item:last-child {
  padding-bottom: 0em; }

/*******************************
            Types
*******************************/
/*-------------------
      Horizontal
--------------------*/
.ui.horizontal.list {
  display: inline-block;
  font-size: 0em; }

.ui.horizontal.list > .item {
  display: inline-block;
  margin-left: 1em;
  font-size: 1rem; }

.ui.horizontal.list:not(.celled) > .item:first-child {
  margin-left: 0em !important;
  padding-left: 0em !important; }

.ui.horizontal.list .list {
  padding-left: 0em;
  padding-bottom: 0em; }

.ui.horizontal.list > .item > .image,
.ui.horizontal.list .list > .item > .image,
.ui.horizontal.list > .item > .icon,
.ui.horizontal.list .list > .item > .icon,
.ui.horizontal.list > .item > .content,
.ui.horizontal.list .list > .item > .content {
  vertical-align: middle; }

/* Padding on all elements */
.ui.horizontal.list > .item:first-child,
.ui.horizontal.list > .item:last-child {
  padding-top: 0.21428571em;
  padding-bottom: 0.21428571em; }

/* Horizontal List */
.ui.horizontal.list > .item > i.icon {
  margin: 0em;
  padding: 0em 0.25em 0em 0em; }

.ui.horizontal.list > .item > .icon,
.ui.horizontal.list > .item > .icon + .content {
  float: none;
  display: inline-block; }

/*******************************
         Site Overrides
*******************************/
/*!
 * # Semantic UI 2.3.1 - Breadcrumb
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */
/*******************************
           Breadcrumb
*******************************/
.ui.breadcrumb {
  line-height: 1;
  display: inline-block;
  margin: 0em 0em;
  vertical-align: middle; }

.ui.breadcrumb:first-child {
  margin-top: 0em; }

.ui.breadcrumb:last-child {
  margin-bottom: 0em; }

/*******************************
          Content
*******************************/
/* Divider */
.ui.breadcrumb .divider {
  display: inline-block;
  opacity: 0.7;
  margin: 0em 0.21428571rem 0em;
  font-size: 0.92857143em;
  color: rgba(0, 0, 0, 0.4);
  vertical-align: baseline; }

/* Link */
.ui.breadcrumb a {
  color: #4183C4; }

.ui.breadcrumb a:hover {
  color: #1e70bf; }

/* Icon Divider */
.ui.breadcrumb .icon.divider {
  font-size: 0.85714286em;
  vertical-align: baseline; }

/* Section */
.ui.breadcrumb a.section {
  cursor: pointer; }

.ui.breadcrumb .section {
  display: inline-block;
  margin: 0em;
  padding: 0em; }

/* Loose Coupling */
.ui.breadcrumb.segment {
  display: inline-block;
  padding: 0.78571429em 1em; }

/*******************************
            States
*******************************/
.ui.breadcrumb .active.section {
  font-weight: bold; }

/*******************************
           Variations
*******************************/
.ui.mini.breadcrumb {
  font-size: 0.78571429rem; }

.ui.tiny.breadcrumb {
  font-size: 0.85714286rem; }

.ui.small.breadcrumb {
  font-size: 0.92857143rem; }

.ui.breadcrumb {
  font-size: 1rem; }

.ui.large.breadcrumb {
  font-size: 1.14285714rem; }

.ui.big.breadcrumb {
  font-size: 1.28571429rem; }

.ui.huge.breadcrumb {
  font-size: 1.42857143rem; }

.ui.massive.breadcrumb {
  font-size: 1.71428571rem; }

/*******************************
         Theme Overrides
*******************************/
/*******************************
         Site Overrides
*******************************/
/*!
 * # Semantic UI 2.3.1 - Form
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */
/*******************************
            Elements
*******************************/
/*--------------------
        Form
---------------------*/
.ui.form {
  position: relative;
  max-width: 100%; }

/*--------------------
        Content
---------------------*/
.ui.form > p {
  margin: 1em 0em; }

/*--------------------
        Field
---------------------*/
.ui.form .field {
  clear: both;
  margin: 0em 0em 1em; }

.ui.form .field:last-child,
.ui.form .fields:last-child .field {
  margin-bottom: 0em; }

.ui.form .fields .field {
  clear: both;
  margin: 0em; }

/*--------------------
        Labels
---------------------*/
.ui.form .field > label {
  display: block;
  margin: 0em 0em 0.28571429rem 0em;
  color: rgba(0, 0, 0, 0.87);
  font-size: 14px;
  text-transform: none;
  margin-bottom: 10px !important; }

/*--------------------
    Standard Inputs
---------------------*/
.ui.form textarea,
.ui.form input:not([type]),
.ui.form input[type="date"],
.ui.form input[type="datetime-local"],
.ui.form input[type="email"],
.ui.form input[type="number"],
.ui.form input[type="password"],
.ui.form input[type="search"],
.ui.form input[type="tel"],
.ui.form input[type="time"],
.ui.form input[type="text"],
.ui.form input[type="file"],
.ui.form input[type="url"] {
  width: 100%;
  vertical-align: top; }

/* Set max height on unusual input */
.ui.form ::-webkit-datetime-edit,
.ui.form ::-webkit-inner-spin-button {
  height: 1.21428571em; }

.ui.form input:not([type]),
.ui.form input[type="date"],
.ui.form input[type="datetime-local"],
.ui.form input[type="email"],
.ui.form input[type="number"],
.ui.form input[type="password"],
.ui.form input[type="search"],
.ui.form input[type="tel"],
.ui.form input[type="time"],
.ui.form input[type="text"],
.ui.form input[type="file"],
.ui.form input[type="url"] {
  margin: 0em;
  outline: none;
  -webkit-appearance: none;
  tap-highlight-color: rgba(255, 255, 255, 0);
  line-height: 1.21428571em;
  padding: 15px 15px 15px 20px;
  font-size: 1em;
  background: #f6f7f8;
  border: 1px solid #f6f7f8;
  color: #323232;
  border-radius: 0;
  -webkit-box-shadow: 0em 0em 0em 0em transparent inset;
  box-shadow: 0em 0em 0em 0em transparent inset;
  -webkit-transition: color 0.1s ease, border-color 0.1s ease;
  transition: color 0.1s ease, border-color 0.1s ease;
  transition: all 0.5s ease-in-out; }

.ui.form input::-moz-placeholder {
  color: #323232; }

.ui.form input::-ms-input-placeholder {
  color: #323232; }

.ui.form input::-webkit-input-placeholder {
  color: #323232; }

/* Text Area */
.ui.form textarea {
  margin: 0em;
  -webkit-appearance: none;
  tap-highlight-color: rgba(255, 255, 255, 0);
  padding: 0.78571429em 1em;
  background: #FFFFFF;
  border: 1px solid rgba(34, 36, 38, 0.15);
  outline: none;
  color: rgba(0, 0, 0, 0.87);
  border-radius: 0.28571429rem;
  -webkit-box-shadow: 0em 0em 0em 0em transparent inset;
  box-shadow: 0em 0em 0em 0em transparent inset;
  -webkit-transition: color 0.1s ease, border-color 0.1s ease;
  transition: color 0.1s ease, border-color 0.1s ease;
  font-size: 1em;
  line-height: 1.2857;
  resize: vertical; }

.ui.form textarea:not([rows]) {
  height: 12em;
  min-height: 8em;
  max-height: 24em; }

.ui.form textarea,
.ui.form input[type="checkbox"] {
  vertical-align: top; }

/*--------------------------
  Input w/ attached Button
---------------------------*/
.ui.form input.attached {
  width: auto; }

/*--------------------
     Basic Select
---------------------*/
.ui.form select {
  display: block;
  height: auto;
  width: 100%;
  background: #FFFFFF;
  border: 1px solid rgba(34, 36, 38, 0.15);
  border-radius: 0.28571429rem;
  -webkit-box-shadow: 0em 0em 0em 0em transparent inset;
  box-shadow: 0em 0em 0em 0em transparent inset;
  padding: 0.62em 1em;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: color 0.1s ease, border-color 0.1s ease;
  transition: color 0.1s ease, border-color 0.1s ease; }

/*--------------------
       Dropdown
---------------------*/
/* Block */
.ui.form .field > .selection.dropdown {
  width: 100%; }

.ui.form .field > .selection.dropdown > .dropdown.icon {
  float: right; }

/* Inline */
.ui.form .inline.fields .field > .selection.dropdown,
.ui.form .inline.field > .selection.dropdown {
  width: auto; }

.ui.form .inline.fields .field > .selection.dropdown > .dropdown.icon,
.ui.form .inline.field > .selection.dropdown > .dropdown.icon {
  float: none; }

/*--------------------
       UI Input
---------------------*/
/* Block */
.ui.form .field .ui.input,
.ui.form .fields .field .ui.input,
.ui.form .wide.field .ui.input {
  width: 100%; }

/* Inline  */
.ui.form .inline.fields .field:not(.wide) .ui.input,
.ui.form .inline.field:not(.wide) .ui.input {
  width: auto;
  vertical-align: middle; }

/* Full Width Input */
.ui.form .ten.fields .ui.input input,
.ui.form .nine.fields .ui.input input,
.ui.form .eight.fields .ui.input input,
.ui.form .seven.fields .ui.input input,
.ui.form .six.fields .ui.input input,
.ui.form .five.fields .ui.input input,
.ui.form .four.fields .ui.input input,
.ui.form .three.fields .ui.input input,
.ui.form .two.fields .ui.input input,
.ui.form .wide.field .ui.input input {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
  width: 0px; }

/*--------------------
   Types of Messages
---------------------*/
.ui.form .success.message,
.ui.form .warning.message,
.ui.form .error.message {
  display: none; }

/* Assumptions */
.ui.form .message:first-child {
  margin-top: 0px; }

/*--------------------
   Validation Prompt
---------------------*/
.ui.form .field .prompt.label {
  white-space: normal;
  background: #FFFFFF !important;
  border: 1px solid #E0B4B4 !important;
  color: #9F3A38 !important; }

.ui.form .inline.fields .field .prompt,
.ui.form .inline.field .prompt {
  vertical-align: top;
  margin: -0.25em 0em -0.5em 0.5em; }

.ui.form .inline.fields .field .prompt:before,
.ui.form .inline.field .prompt:before {
  border-width: 0px 0px 1px 1px;
  bottom: auto;
  right: auto;
  top: 50%;
  left: 0em; }

/*-------------------
     Column Count
--------------------*/
/* Assume full width with one column */
.ui.grid > .column:only-child,
.ui.grid > .row > .column:only-child {
  width: 100%; }

/* Grid Based */
.ui[class*="one column"].grid > .row > .column,
.ui[class*="one column"].grid > .column:not(.row) {
  width: 100%; }

.ui[class*="two column"].grid > .row > .column,
.ui[class*="two column"].grid > .column:not(.row) {
  width: 50%; }

.ui[class*="three column"].grid > .row > .column,
.ui[class*="three column"].grid > .column:not(.row) {
  width: 33.33333333%; }

.ui[class*="four column"].grid > .row > .column,
.ui[class*="four column"].grid > .column:not(.row) {
  width: 25%; }

.ui[class*="five column"].grid > .row > .column,
.ui[class*="five column"].grid > .column:not(.row) {
  width: 20%; }

.ui[class*="six column"].grid > .row > .column,
.ui[class*="six column"].grid > .column:not(.row) {
  width: 16.66666667%; }

.ui[class*="seven column"].grid > .row > .column,
.ui[class*="seven column"].grid > .column:not(.row) {
  width: 14.28571429%; }

.ui[class*="eight column"].grid > .row > .column,
.ui[class*="eight column"].grid > .column:not(.row) {
  width: 12.5%; }

.ui[class*="nine column"].grid > .row > .column,
.ui[class*="nine column"].grid > .column:not(.row) {
  width: 11.11111111%; }

.ui[class*="ten column"].grid > .row > .column,
.ui[class*="ten column"].grid > .column:not(.row) {
  width: 10%; }

.ui[class*="eleven column"].grid > .row > .column,
.ui[class*="eleven column"].grid > .column:not(.row) {
  width: 9.09090909%; }

.ui[class*="twelve column"].grid > .row > .column,
.ui[class*="twelve column"].grid > .column:not(.row) {
  width: 8.33333333%; }

.ui[class*="thirteen column"].grid > .row > .column,
.ui[class*="thirteen column"].grid > .column:not(.row) {
  width: 7.69230769%; }

.ui[class*="fourteen column"].grid > .row > .column,
.ui[class*="fourteen column"].grid > .column:not(.row) {
  width: 7.14285714%; }

.ui[class*="fifteen column"].grid > .row > .column,
.ui[class*="fifteen column"].grid > .column:not(.row) {
  width: 6.66666667%; }

.ui[class*="sixteen column"].grid > .row > .column,
.ui[class*="sixteen column"].grid > .column:not(.row) {
  width: 6.25%; }

/* Row Based Overrides */
.ui.grid > [class*="one column"].row > .column {
  width: 100% !important; }

.ui.grid > [class*="two column"].row > .column {
  width: 50% !important; }

.ui.grid > [class*="three column"].row > .column {
  width: 33.33333333% !important; }

.ui.grid > [class*="four column"].row > .column {
  width: 25% !important; }

.ui.grid > [class*="five column"].row > .column {
  width: 20% !important; }

.ui.grid > [class*="six column"].row > .column {
  width: 16.66666667% !important; }

.ui.grid > [class*="seven column"].row > .column {
  width: 14.28571429% !important; }

.ui.grid > [class*="eight column"].row > .column {
  width: 12.5% !important; }

.ui.grid > [class*="nine column"].row > .column {
  width: 11.11111111% !important; }

.ui.grid > [class*="ten column"].row > .column {
  width: 10% !important; }

.ui.grid > [class*="eleven column"].row > .column {
  width: 9.09090909% !important; }

.ui.grid > [class*="twelve column"].row > .column {
  width: 8.33333333% !important; }

.ui.grid > [class*="thirteen column"].row > .column {
  width: 7.69230769% !important; }

.ui.grid > [class*="fourteen column"].row > .column {
  width: 7.14285714% !important; }

.ui.grid > [class*="fifteen column"].row > .column {
  width: 6.66666667% !important; }

.ui.grid > [class*="sixteen column"].row > .column {
  width: 6.25% !important; }

/* Celled Page */
.ui.celled.page.grid {
  -webkit-box-shadow: none;
  box-shadow: none; }

/*-------------------
    Column Width
--------------------*/
/* Sizing Combinations */
.ui.grid > .row > [class*="one wide"].column,
.ui.grid > .column.row > [class*="one wide"].column,
.ui.grid > [class*="one wide"].column,
.ui.column.grid > [class*="one wide"].column {
  width: 6.25% !important; }

.ui.grid > .row > [class*="two wide"].column,
.ui.grid > .column.row > [class*="two wide"].column,
.ui.grid > [class*="two wide"].column,
.ui.column.grid > [class*="two wide"].column {
  width: 12.5% !important; }

.ui.grid > .row > [class*="three wide"].column,
.ui.grid > .column.row > [class*="three wide"].column,
.ui.grid > [class*="three wide"].column,
.ui.column.grid > [class*="three wide"].column {
  width: 18.75% !important; }

.ui.grid > .row > [class*="four wide"].column,
.ui.grid > .column.row > [class*="four wide"].column,
.ui.grid > [class*="four wide"].column,
.ui.column.grid > [class*="four wide"].column {
  width: 25% !important; }

.ui.grid > .row > [class*="five wide"].column,
.ui.grid > .column.row > [class*="five wide"].column,
.ui.grid > [class*="five wide"].column,
.ui.column.grid > [class*="five wide"].column {
  width: 31.25% !important; }

.ui.grid > .row > [class*="six wide"].column,
.ui.grid > .column.row > [class*="six wide"].column,
.ui.grid > [class*="six wide"].column,
.ui.column.grid > [class*="six wide"].column {
  width: 37.5% !important; }

.ui.grid > .row > [class*="seven wide"].column,
.ui.grid > .column.row > [class*="seven wide"].column,
.ui.grid > [class*="seven wide"].column,
.ui.column.grid > [class*="seven wide"].column {
  width: 43.75% !important; }

.ui.grid > .row > [class*="eight wide"].column,
.ui.grid > .column.row > [class*="eight wide"].column,
.ui.grid > [class*="eight wide"].column,
.ui.column.grid > [class*="eight wide"].column {
  width: 50% !important; }

.ui.grid > .row > [class*="nine wide"].column,
.ui.grid > .column.row > [class*="nine wide"].column,
.ui.grid > [class*="nine wide"].column,
.ui.column.grid > [class*="nine wide"].column {
  width: 56.25% !important; }

.ui.grid > .row > [class*="ten wide"].column,
.ui.grid > .column.row > [class*="ten wide"].column,
.ui.grid > [class*="ten wide"].column,
.ui.column.grid > [class*="ten wide"].column {
  width: 62.5% !important; }

.ui.grid > .row > [class*="eleven wide"].column,
.ui.grid > .column.row > [class*="eleven wide"].column,
.ui.grid > [class*="eleven wide"].column,
.ui.column.grid > [class*="eleven wide"].column {
  width: 68.75% !important; }

.ui.grid > .row > [class*="twelve wide"].column,
.ui.grid > .column.row > [class*="twelve wide"].column,
.ui.grid > [class*="twelve wide"].column,
.ui.column.grid > [class*="twelve wide"].column {
  width: 100% !important; }

.ui.grid > .row > [class*="thirteen wide"].column,
.ui.grid > .column.row > [class*="thirteen wide"].column,
.ui.grid > [class*="thirteen wide"].column,
.ui.column.grid > [class*="thirteen wide"].column {
  width: 81.25% !important; }

.ui.grid > .row > [class*="fourteen wide"].column,
.ui.grid > .column.row > [class*="fourteen wide"].column,
.ui.grid > [class*="fourteen wide"].column,
.ui.column.grid > [class*="fourteen wide"].column {
  width: 87.5% !important; }

.ui.grid > .row > [class*="fifteen wide"].column,
.ui.grid > .column.row > [class*="fifteen wide"].column,
.ui.grid > [class*="fifteen wide"].column,
.ui.column.grid > [class*="fifteen wide"].column {
  width: 93.75% !important; }

.ui.grid > .row > [class*="sixteen wide"].column,
.ui.grid > .column.row > [class*="sixteen wide"].column,
.ui.grid > [class*="sixteen wide"].column,
.ui.column.grid > [class*="sixteen wide"].column {
  width: 100% !important; }

/*----------------------
    Width per Device
-----------------------*/
/* Mobile Sizing Combinations */
@media only screen and (min-width: 320px) and (max-width: 767px) {
  .ui.grid > .row > [class*="one wide mobile"].column,
  .ui.grid > .column.row > [class*="one wide mobile"].column,
  .ui.grid > [class*="one wide mobile"].column,
  .ui.column.grid > [class*="one wide mobile"].column {
    width: 6.25% !important; }
  .ui.grid > .row > [class*="two wide mobile"].column,
  .ui.grid > .column.row > [class*="two wide mobile"].column,
  .ui.grid > [class*="two wide mobile"].column,
  .ui.column.grid > [class*="two wide mobile"].column {
    width: 12.5% !important; }
  .ui.grid > .row > [class*="three wide mobile"].column,
  .ui.grid > .column.row > [class*="three wide mobile"].column,
  .ui.grid > [class*="three wide mobile"].column,
  .ui.column.grid > [class*="three wide mobile"].column {
    width: 18.75% !important; }
  .ui.grid > .row > [class*="four wide mobile"].column,
  .ui.grid > .column.row > [class*="four wide mobile"].column,
  .ui.grid > [class*="four wide mobile"].column,
  .ui.column.grid > [class*="four wide mobile"].column {
    width: 25% !important; }
  .ui.grid > .row > [class*="five wide mobile"].column,
  .ui.grid > .column.row > [class*="five wide mobile"].column,
  .ui.grid > [class*="five wide mobile"].column,
  .ui.column.grid > [class*="five wide mobile"].column {
    width: 31.25% !important; }
  .ui.grid > .row > [class*="six wide mobile"].column,
  .ui.grid > .column.row > [class*="six wide mobile"].column,
  .ui.grid > [class*="six wide mobile"].column,
  .ui.column.grid > [class*="six wide mobile"].column {
    width: 37.5% !important; }
  .ui.grid > .row > [class*="seven wide mobile"].column,
  .ui.grid > .column.row > [class*="seven wide mobile"].column,
  .ui.grid > [class*="seven wide mobile"].column,
  .ui.column.grid > [class*="seven wide mobile"].column {
    width: 43.75% !important; }
  .ui.grid > .row > [class*="eight wide mobile"].column,
  .ui.grid > .column.row > [class*="eight wide mobile"].column,
  .ui.grid > [class*="eight wide mobile"].column,
  .ui.column.grid > [class*="eight wide mobile"].column {
    width: 50% !important; }
  .ui.grid > .row > [class*="nine wide mobile"].column,
  .ui.grid > .column.row > [class*="nine wide mobile"].column,
  .ui.grid > [class*="nine wide mobile"].column,
  .ui.column.grid > [class*="nine wide mobile"].column {
    width: 56.25% !important; }
  .ui.grid > .row > [class*="ten wide mobile"].column,
  .ui.grid > .column.row > [class*="ten wide mobile"].column,
  .ui.grid > [class*="ten wide mobile"].column,
  .ui.column.grid > [class*="ten wide mobile"].column {
    width: 62.5% !important; }
  .ui.grid > .row > [class*="eleven wide mobile"].column,
  .ui.grid > .column.row > [class*="eleven wide mobile"].column,
  .ui.grid > [class*="eleven wide mobile"].column,
  .ui.column.grid > [class*="eleven wide mobile"].column {
    width: 68.75% !important; }
  .ui.grid > .row > [class*="twelve wide mobile"].column,
  .ui.grid > .column.row > [class*="twelve wide mobile"].column,
  .ui.grid > [class*="twelve wide mobile"].column,
  .ui.column.grid > [class*="twelve wide mobile"].column {
    width: 75% !important; }
  .ui.grid > .row > [class*="thirteen wide mobile"].column,
  .ui.grid > .column.row > [class*="thirteen wide mobile"].column,
  .ui.grid > [class*="thirteen wide mobile"].column,
  .ui.column.grid > [class*="thirteen wide mobile"].column {
    width: 81.25% !important; }
  .ui.grid > .row > [class*="fourteen wide mobile"].column,
  .ui.grid > .column.row > [class*="fourteen wide mobile"].column,
  .ui.grid > [class*="fourteen wide mobile"].column,
  .ui.column.grid > [class*="fourteen wide mobile"].column {
    width: 87.5% !important; }
  .ui.grid > .row > [class*="fifteen wide mobile"].column,
  .ui.grid > .column.row > [class*="fifteen wide mobile"].column,
  .ui.grid > [class*="fifteen wide mobile"].column,
  .ui.column.grid > [class*="fifteen wide mobile"].column {
    width: 93.75% !important; }
  .ui.grid > .row > [class*="sixteen wide mobile"].column,
  .ui.grid > .column.row > [class*="sixteen wide mobile"].column,
  .ui.grid > [class*="sixteen wide mobile"].column,
  .ui.column.grid > [class*="sixteen wide mobile"].column {
    width: 100% !important; } }

/* Tablet Sizing Combinations */
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .ui.grid > .row > [class*="one wide tablet"].column,
  .ui.grid > .column.row > [class*="one wide tablet"].column,
  .ui.grid > [class*="one wide tablet"].column,
  .ui.column.grid > [class*="one wide tablet"].column {
    width: 6.25% !important; }
  .ui.grid > .row > [class*="two wide tablet"].column,
  .ui.grid > .column.row > [class*="two wide tablet"].column,
  .ui.grid > [class*="two wide tablet"].column,
  .ui.column.grid > [class*="two wide tablet"].column {
    width: 12.5% !important; }
  .ui.grid > .row > [class*="three wide tablet"].column,
  .ui.grid > .column.row > [class*="three wide tablet"].column,
  .ui.grid > [class*="three wide tablet"].column,
  .ui.column.grid > [class*="three wide tablet"].column {
    width: 18.75% !important; }
  .ui.grid > .row > [class*="four wide tablet"].column,
  .ui.grid > .column.row > [class*="four wide tablet"].column,
  .ui.grid > [class*="four wide tablet"].column,
  .ui.column.grid > [class*="four wide tablet"].column {
    width: 25% !important; }
  .ui.grid > .row > [class*="five wide tablet"].column,
  .ui.grid > .column.row > [class*="five wide tablet"].column,
  .ui.grid > [class*="five wide tablet"].column,
  .ui.column.grid > [class*="five wide tablet"].column {
    width: 31.25% !important; }
  .ui.grid > .row > [class*="six wide tablet"].column,
  .ui.grid > .column.row > [class*="six wide tablet"].column,
  .ui.grid > [class*="six wide tablet"].column,
  .ui.column.grid > [class*="six wide tablet"].column {
    width: 37.5% !important; }
  .ui.grid > .row > [class*="seven wide tablet"].column,
  .ui.grid > .column.row > [class*="seven wide tablet"].column,
  .ui.grid > [class*="seven wide tablet"].column,
  .ui.column.grid > [class*="seven wide tablet"].column {
    width: 43.75% !important; }
  .ui.grid > .row > [class*="eight wide tablet"].column,
  .ui.grid > .column.row > [class*="eight wide tablet"].column,
  .ui.grid > [class*="eight wide tablet"].column,
  .ui.column.grid > [class*="eight wide tablet"].column {
    width: 50% !important; }
  .ui.grid > .row > [class*="nine wide tablet"].column,
  .ui.grid > .column.row > [class*="nine wide tablet"].column,
  .ui.grid > [class*="nine wide tablet"].column,
  .ui.column.grid > [class*="nine wide tablet"].column {
    width: 56.25% !important; }
  .ui.grid > .row > [class*="ten wide tablet"].column,
  .ui.grid > .column.row > [class*="ten wide tablet"].column,
  .ui.grid > [class*="ten wide tablet"].column,
  .ui.column.grid > [class*="ten wide tablet"].column {
    width: 62.5% !important; }
  .ui.grid > .row > [class*="eleven wide tablet"].column,
  .ui.grid > .column.row > [class*="eleven wide tablet"].column,
  .ui.grid > [class*="eleven wide tablet"].column,
  .ui.column.grid > [class*="eleven wide tablet"].column {
    width: 68.75% !important; }
  .ui.grid > .row > [class*="twelve wide tablet"].column,
  .ui.grid > .column.row > [class*="twelve wide tablet"].column,
  .ui.grid > [class*="twelve wide tablet"].column,
  .ui.column.grid > [class*="twelve wide tablet"].column {
    width: 75% !important; }
  .ui.grid > .row > [class*="thirteen wide tablet"].column,
  .ui.grid > .column.row > [class*="thirteen wide tablet"].column,
  .ui.grid > [class*="thirteen wide tablet"].column,
  .ui.column.grid > [class*="thirteen wide tablet"].column {
    width: 81.25% !important; }
  .ui.grid > .row > [class*="fourteen wide tablet"].column,
  .ui.grid > .column.row > [class*="fourteen wide tablet"].column,
  .ui.grid > [class*="fourteen wide tablet"].column,
  .ui.column.grid > [class*="fourteen wide tablet"].column {
    width: 87.5% !important; }
  .ui.grid > .row > [class*="fifteen wide tablet"].column,
  .ui.grid > .column.row > [class*="fifteen wide tablet"].column,
  .ui.grid > [class*="fifteen wide tablet"].column,
  .ui.column.grid > [class*="fifteen wide tablet"].column {
    width: 93.75% !important; }
  .ui.grid > .row > [class*="sixteen wide tablet"].column,
  .ui.grid > .column.row > [class*="sixteen wide tablet"].column,
  .ui.grid > [class*="sixteen wide tablet"].column,
  .ui.column.grid > [class*="sixteen wide tablet"].column {
    width: 100% !important; } }

/* Computer/Desktop Sizing Combinations */
@media only screen and (min-width: 992px) {
  .ui.grid > .row > [class*="one wide computer"].column,
  .ui.grid > .column.row > [class*="one wide computer"].column,
  .ui.grid > [class*="one wide computer"].column,
  .ui.column.grid > [class*="one wide computer"].column {
    width: 6.25% !important; }
  .ui.grid > .row > [class*="two wide computer"].column,
  .ui.grid > .column.row > [class*="two wide computer"].column,
  .ui.grid > [class*="two wide computer"].column,
  .ui.column.grid > [class*="two wide computer"].column {
    width: 12.5% !important; }
  .ui.grid > .row > [class*="three wide computer"].column,
  .ui.grid > .column.row > [class*="three wide computer"].column,
  .ui.grid > [class*="three wide computer"].column,
  .ui.column.grid > [class*="three wide computer"].column {
    width: 18.75% !important; }
  .ui.grid > .row > [class*="four wide computer"].column,
  .ui.grid > .column.row > [class*="four wide computer"].column,
  .ui.grid > [class*="four wide computer"].column,
  .ui.column.grid > [class*="four wide computer"].column {
    width: 25% !important; }
  .ui.grid > .row > [class*="five wide computer"].column,
  .ui.grid > .column.row > [class*="five wide computer"].column,
  .ui.grid > [class*="five wide computer"].column,
  .ui.column.grid > [class*="five wide computer"].column {
    width: 31.25% !important; }
  .ui.grid > .row > [class*="six wide computer"].column,
  .ui.grid > .column.row > [class*="six wide computer"].column,
  .ui.grid > [class*="six wide computer"].column,
  .ui.column.grid > [class*="six wide computer"].column {
    width: 37.5% !important; }
  .ui.grid > .row > [class*="seven wide computer"].column,
  .ui.grid > .column.row > [class*="seven wide computer"].column,
  .ui.grid > [class*="seven wide computer"].column,
  .ui.column.grid > [class*="seven wide computer"].column {
    width: 43.75% !important; }
  .ui.grid > .row > [class*="eight wide computer"].column,
  .ui.grid > .column.row > [class*="eight wide computer"].column,
  .ui.grid > [class*="eight wide computer"].column,
  .ui.column.grid > [class*="eight wide computer"].column {
    width: 50% !important; }
  .ui.grid > .row > [class*="nine wide computer"].column,
  .ui.grid > .column.row > [class*="nine wide computer"].column,
  .ui.grid > [class*="nine wide computer"].column,
  .ui.column.grid > [class*="nine wide computer"].column {
    width: 56.25% !important; }
  .ui.grid > .row > [class*="ten wide computer"].column,
  .ui.grid > .column.row > [class*="ten wide computer"].column,
  .ui.grid > [class*="ten wide computer"].column,
  .ui.column.grid > [class*="ten wide computer"].column {
    width: 62.5% !important; }
  .ui.grid > .row > [class*="eleven wide computer"].column,
  .ui.grid > .column.row > [class*="eleven wide computer"].column,
  .ui.grid > [class*="eleven wide computer"].column,
  .ui.column.grid > [class*="eleven wide computer"].column {
    width: 68.75% !important; }
  .ui.grid > .row > [class*="twelve wide computer"].column,
  .ui.grid > .column.row > [class*="twelve wide computer"].column,
  .ui.grid > [class*="twelve wide computer"].column,
  .ui.column.grid > [class*="twelve wide computer"].column {
    width: 75% !important; }
  .ui.grid > .row > [class*="thirteen wide computer"].column,
  .ui.grid > .column.row > [class*="thirteen wide computer"].column,
  .ui.grid > [class*="thirteen wide computer"].column,
  .ui.column.grid > [class*="thirteen wide computer"].column {
    width: 81.25% !important; }
  .ui.grid > .row > [class*="fourteen wide computer"].column,
  .ui.grid > .column.row > [class*="fourteen wide computer"].column,
  .ui.grid > [class*="fourteen wide computer"].column,
  .ui.column.grid > [class*="fourteen wide computer"].column {
    width: 87.5% !important; }
  .ui.grid > .row > [class*="fifteen wide computer"].column,
  .ui.grid > .column.row > [class*="fifteen wide computer"].column,
  .ui.grid > [class*="fifteen wide computer"].column,
  .ui.column.grid > [class*="fifteen wide computer"].column {
    width: 93.75% !important; }
  .ui.grid > .row > [class*="sixteen wide computer"].column,
  .ui.grid > .column.row > [class*="sixteen wide computer"].column,
  .ui.grid > [class*="sixteen wide computer"].column,
  .ui.column.grid > [class*="sixteen wide computer"].column {
    width: 100% !important; } }

/* Large Monitor Sizing Combinations */
@media only screen and (min-width: 1200px) and (max-width: 1919px) {
  .ui.grid > .row > [class*="one wide large screen"].column,
  .ui.grid > .column.row > [class*="one wide large screen"].column,
  .ui.grid > [class*="one wide large screen"].column,
  .ui.column.grid > [class*="one wide large screen"].column {
    width: 6.25% !important; }
  .ui.grid > .row > [class*="two wide large screen"].column,
  .ui.grid > .column.row > [class*="two wide large screen"].column,
  .ui.grid > [class*="two wide large screen"].column,
  .ui.column.grid > [class*="two wide large screen"].column {
    width: 12.5% !important; }
  .ui.grid > .row > [class*="three wide large screen"].column,
  .ui.grid > .column.row > [class*="three wide large screen"].column,
  .ui.grid > [class*="three wide large screen"].column,
  .ui.column.grid > [class*="three wide large screen"].column {
    width: 18.75% !important; }
  .ui.grid > .row > [class*="four wide large screen"].column,
  .ui.grid > .column.row > [class*="four wide large screen"].column,
  .ui.grid > [class*="four wide large screen"].column,
  .ui.column.grid > [class*="four wide large screen"].column {
    width: 25% !important; }
  .ui.grid > .row > [class*="five wide large screen"].column,
  .ui.grid > .column.row > [class*="five wide large screen"].column,
  .ui.grid > [class*="five wide large screen"].column,
  .ui.column.grid > [class*="five wide large screen"].column {
    width: 31.25% !important; }
  .ui.grid > .row > [class*="six wide large screen"].column,
  .ui.grid > .column.row > [class*="six wide large screen"].column,
  .ui.grid > [class*="six wide large screen"].column,
  .ui.column.grid > [class*="six wide large screen"].column {
    width: 37.5% !important; }
  .ui.grid > .row > [class*="seven wide large screen"].column,
  .ui.grid > .column.row > [class*="seven wide large screen"].column,
  .ui.grid > [class*="seven wide large screen"].column,
  .ui.column.grid > [class*="seven wide large screen"].column {
    width: 43.75% !important; }
  .ui.grid > .row > [class*="eight wide large screen"].column,
  .ui.grid > .column.row > [class*="eight wide large screen"].column,
  .ui.grid > [class*="eight wide large screen"].column,
  .ui.column.grid > [class*="eight wide large screen"].column {
    width: 50% !important; }
  .ui.grid > .row > [class*="nine wide large screen"].column,
  .ui.grid > .column.row > [class*="nine wide large screen"].column,
  .ui.grid > [class*="nine wide large screen"].column,
  .ui.column.grid > [class*="nine wide large screen"].column {
    width: 56.25% !important; }
  .ui.grid > .row > [class*="ten wide large screen"].column,
  .ui.grid > .column.row > [class*="ten wide large screen"].column,
  .ui.grid > [class*="ten wide large screen"].column,
  .ui.column.grid > [class*="ten wide large screen"].column {
    width: 62.5% !important; }
  .ui.grid > .row > [class*="eleven wide large screen"].column,
  .ui.grid > .column.row > [class*="eleven wide large screen"].column,
  .ui.grid > [class*="eleven wide large screen"].column,
  .ui.column.grid > [class*="eleven wide large screen"].column {
    width: 68.75% !important; }
  .ui.grid > .row > [class*="twelve wide large screen"].column,
  .ui.grid > .column.row > [class*="twelve wide large screen"].column,
  .ui.grid > [class*="twelve wide large screen"].column,
  .ui.column.grid > [class*="twelve wide large screen"].column {
    width: 75% !important; }
  .ui.grid > .row > [class*="thirteen wide large screen"].column,
  .ui.grid > .column.row > [class*="thirteen wide large screen"].column,
  .ui.grid > [class*="thirteen wide large screen"].column,
  .ui.column.grid > [class*="thirteen wide large screen"].column {
    width: 81.25% !important; }
  .ui.grid > .row > [class*="fourteen wide large screen"].column,
  .ui.grid > .column.row > [class*="fourteen wide large screen"].column,
  .ui.grid > [class*="fourteen wide large screen"].column,
  .ui.column.grid > [class*="fourteen wide large screen"].column {
    width: 87.5% !important; }
  .ui.grid > .row > [class*="fifteen wide large screen"].column,
  .ui.grid > .column.row > [class*="fifteen wide large screen"].column,
  .ui.grid > [class*="fifteen wide large screen"].column,
  .ui.column.grid > [class*="fifteen wide large screen"].column {
    width: 93.75% !important; }
  .ui.grid > .row > [class*="sixteen wide large screen"].column,
  .ui.grid > .column.row > [class*="sixteen wide large screen"].column,
  .ui.grid > [class*="sixteen wide large screen"].column,
  .ui.column.grid > [class*="sixteen wide large screen"].column {
    width: 100% !important; } }

/* Widescreen Sizing Combinations */
@media only screen and (min-width: 1920px) {
  .ui.grid > .row > [class*="one wide widescreen"].column,
  .ui.grid > .column.row > [class*="one wide widescreen"].column,
  .ui.grid > [class*="one wide widescreen"].column,
  .ui.column.grid > [class*="one wide widescreen"].column {
    width: 6.25% !important; }
  .ui.grid > .row > [class*="two wide widescreen"].column,
  .ui.grid > .column.row > [class*="two wide widescreen"].column,
  .ui.grid > [class*="two wide widescreen"].column,
  .ui.column.grid > [class*="two wide widescreen"].column {
    width: 12.5% !important; }
  .ui.grid > .row > [class*="three wide widescreen"].column,
  .ui.grid > .column.row > [class*="three wide widescreen"].column,
  .ui.grid > [class*="three wide widescreen"].column,
  .ui.column.grid > [class*="three wide widescreen"].column {
    width: 18.75% !important; }
  .ui.grid > .row > [class*="four wide widescreen"].column,
  .ui.grid > .column.row > [class*="four wide widescreen"].column,
  .ui.grid > [class*="four wide widescreen"].column,
  .ui.column.grid > [class*="four wide widescreen"].column {
    width: 25% !important; }
  .ui.grid > .row > [class*="five wide widescreen"].column,
  .ui.grid > .column.row > [class*="five wide widescreen"].column,
  .ui.grid > [class*="five wide widescreen"].column,
  .ui.column.grid > [class*="five wide widescreen"].column {
    width: 31.25% !important; }
  .ui.grid > .row > [class*="six wide widescreen"].column,
  .ui.grid > .column.row > [class*="six wide widescreen"].column,
  .ui.grid > [class*="six wide widescreen"].column,
  .ui.column.grid > [class*="six wide widescreen"].column {
    width: 37.5% !important; }
  .ui.grid > .row > [class*="seven wide widescreen"].column,
  .ui.grid > .column.row > [class*="seven wide widescreen"].column,
  .ui.grid > [class*="seven wide widescreen"].column,
  .ui.column.grid > [class*="seven wide widescreen"].column {
    width: 43.75% !important; }
  .ui.grid > .row > [class*="eight wide widescreen"].column,
  .ui.grid > .column.row > [class*="eight wide widescreen"].column,
  .ui.grid > [class*="eight wide widescreen"].column,
  .ui.column.grid > [class*="eight wide widescreen"].column {
    width: 50% !important; }
  .ui.grid > .row > [class*="nine wide widescreen"].column,
  .ui.grid > .column.row > [class*="nine wide widescreen"].column,
  .ui.grid > [class*="nine wide widescreen"].column,
  .ui.column.grid > [class*="nine wide widescreen"].column {
    width: 56.25% !important; }
  .ui.grid > .row > [class*="ten wide widescreen"].column,
  .ui.grid > .column.row > [class*="ten wide widescreen"].column,
  .ui.grid > [class*="ten wide widescreen"].column,
  .ui.column.grid > [class*="ten wide widescreen"].column {
    width: 62.5% !important; }
  .ui.grid > .row > [class*="eleven wide widescreen"].column,
  .ui.grid > .column.row > [class*="eleven wide widescreen"].column,
  .ui.grid > [class*="eleven wide widescreen"].column,
  .ui.column.grid > [class*="eleven wide widescreen"].column {
    width: 68.75% !important; }
  .ui.grid > .row > [class*="twelve wide widescreen"].column,
  .ui.grid > .column.row > [class*="twelve wide widescreen"].column,
  .ui.grid > [class*="twelve wide widescreen"].column,
  .ui.column.grid > [class*="twelve wide widescreen"].column {
    width: 75% !important; }
  .ui.grid > .row > [class*="thirteen wide widescreen"].column,
  .ui.grid > .column.row > [class*="thirteen wide widescreen"].column,
  .ui.grid > [class*="thirteen wide widescreen"].column,
  .ui.column.grid > [class*="thirteen wide widescreen"].column {
    width: 81.25% !important; }
  .ui.grid > .row > [class*="fourteen wide widescreen"].column,
  .ui.grid > .column.row > [class*="fourteen wide widescreen"].column,
  .ui.grid > [class*="fourteen wide widescreen"].column,
  .ui.column.grid > [class*="fourteen wide widescreen"].column {
    width: 87.5% !important; }
  .ui.grid > .row > [class*="fifteen wide widescreen"].column,
  .ui.grid > .column.row > [class*="fifteen wide widescreen"].column,
  .ui.grid > [class*="fifteen wide widescreen"].column,
  .ui.column.grid > [class*="fifteen wide widescreen"].column {
    width: 93.75% !important; }
  .ui.grid > .row > [class*="sixteen wide widescreen"].column,
  .ui.grid > .column.row > [class*="sixteen wide widescreen"].column,
  .ui.grid > [class*="sixteen wide widescreen"].column,
  .ui.column.grid > [class*="sixteen wide widescreen"].column {
    width: 100% !important; } }
